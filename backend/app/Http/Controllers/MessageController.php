<?php

namespace App\Http\Controllers;

use App\Events\MessageSent;
use App\Jobs\EchoMessageBack;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class MessageController extends BaseController
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Send a new message.
     */
    public function send(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        // Store the message in the database
        $message = Message::create([
            'user_id' => $request->user()->id,
            'content' => $request->message,
        ]);

        // Load the user relationship for broadcasting
        $message->load('user');

        // Pass the stored message and user to the job
        EchoMessageBack::dispatch($message);

        return response()->json([
            'status' => 'Message sent successfully',
            'message' => [
                'id' => $message->id,
                'content' => $message->content,
                'created_at_unix' => $message->created_at_unix,
                'user' => [
                    'id' => $message->user->id,
                    'username' => $message->user->username,
                ]
            ]
        ]);
    }

    /**
     * Get chat history with pagination.
     */
    public function history(Request $request)
    {
        $request->validate([
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
        ]);

        $perPage = $request->get('per_page', 50);

        $messages = Message::with('user:id,username')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        // Transform the messages to include unix timestamps
        $messages->getCollection()->transform(function ($message) {
            return [
                'id' => $message->id,
                'content' => $message->content,
                'created_at_unix' => $message->created_at_unix,
                'user' => [
                    'id' => $message->user->id,
                    'username' => $message->user->username,
                ]
            ];
        });

        return response()->json($messages);
    }
}
