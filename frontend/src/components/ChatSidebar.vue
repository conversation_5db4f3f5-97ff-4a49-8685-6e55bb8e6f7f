<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import echo from '@/services/echo'
import { useAuthStore } from '@/stores/auth'
import EmojiPicker from './EmojiPicker.vue'
import EmojiAutocomplete from './EmojiAutocomplete.vue'
import { useEmojiAutocomplete } from '@/composables/useEmojiAutocomplete'
import type { Message, MessageHistory } from '@/types/message'
import { formatTimestamp, convertApiMessageToMessage, convertBroadcastMessageToMessage } from '@/types/message'
import axios from 'axios'

// Define emits for parent communication
const emit = defineEmits(['close'])

const messages = ref<Message[]>([])
const newMessage = ref('')
const authStore = useAuthStore()
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLInputElement>()
const emojiPicker = ref<InstanceType<typeof EmojiPicker>>()
const isLoadingHistory = ref(false)

// Emoji functionality
const {
  isVisible: isAutocompleteVisible,
  selectedIndex: autocompleteSelectedIndex,
  filteredEmojis,
  showAutocomplete,
  hideAutocomplete,
  selectNext,
  selectPrevious,
  getSelectedEmoji
} = useEmojiAutocomplete()

const closeChat = () => {
  emit('close')
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const loadChatHistory = async () => {
  if (isLoadingHistory.value) return

  try {
    isLoadingHistory.value = true
    const response = await axios.get<MessageHistory>(`${import.meta.env.VITE_API_URL}/api/messages/history?per_page=50`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    // Convert API messages to internal format and reverse to show oldest first
    const historyMessages = response.data.data
      .map(msg => convertApiMessageToMessage(msg, authStore.user?.id || 0))
      .reverse()

    messages.value = historyMessages
    scrollToBottom()
  } catch (error) {
    console.error('Failed to load chat history:', error)
  } finally {
    isLoadingHistory.value = false
  }
}

const sendMessage = async () => {
  if (!newMessage.value.trim()) return

  try {
    const messageText = newMessage.value

    // Clear input immediately
    newMessage.value = ''
    hideAutocomplete()

    // Send to server
    await axios.post(`${import.meta.env.VITE_API_URL}/api/messages`, {
      message: messageText
    }, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    // The message will be added via the real-time broadcast
    // No need to add it manually here since we'll receive it back via Echo
  } catch (error) {
    console.error('Failed to send message:', error)
    // Restore message on error
    newMessage.value = messageText
  }
}

// Emoji picker functions
const toggleEmojiPicker = () => {
  if (emojiPicker.value) {
    emojiPicker.value.show()
  }
}

const onEmojiSelected = (emoji: string) => {
  newMessage.value += emoji
  if (messageInput.value) {
    messageInput.value.focus()
  }
}

// Autocomplete functions
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value
  const cursorPosition = target.selectionStart || 0

  // Look for emoji shortcode pattern (:word)
  const beforeCursor = value.substring(0, cursorPosition)
  const match = beforeCursor.match(/:([a-zA-Z_]+)$/)

  if (match) {
    const query = match[1]
    showAutocomplete(query)
  } else {
    hideAutocomplete()
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (isAutocompleteVisible.value && filteredEmojis.value.length > 0) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        selectNext()
        break
      case 'ArrowUp':
        event.preventDefault()
        selectPrevious()
        break
      case 'Tab':
      case 'Enter':
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault()
          const selectedEmoji = getSelectedEmoji()
          if (selectedEmoji) {
            insertSelectedEmoji(selectedEmoji.emoji)
          } else {
            sendMessage()
          }
        } else if (event.key === 'Tab') {
          event.preventDefault()
          const selectedEmoji = getSelectedEmoji()
          if (selectedEmoji) {
            insertSelectedEmoji(selectedEmoji.emoji)
          }
        }
        break
      case 'Escape':
        event.preventDefault()
        hideAutocomplete()
        break
    }
  } else if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const insertSelectedEmoji = (emoji: string) => {
  if (!messageInput.value) return

  const cursorPosition = messageInput.value.selectionStart || 0
  const value = newMessage.value
  const beforeCursor = value.substring(0, cursorPosition)
  const afterCursor = value.substring(cursorPosition)

  // Find the start of the emoji shortcode
  const match = beforeCursor.match(/:([a-zA-Z_]+)$/)
  if (match) {
    const startPos = cursorPosition - match[0].length
    newMessage.value = value.substring(0, startPos) + emoji + afterCursor

    // Set cursor position after the emoji
    nextTick(() => {
      if (messageInput.value) {
        const newCursorPos = startPos + emoji.length
        messageInput.value.setSelectionRange(newCursorPos, newCursorPos)
        messageInput.value.focus()
      }
    })
  }

  hideAutocomplete()
}

const onAutocompleteEmojiSelected = (emoji: any) => {
  insertSelectedEmoji(emoji.emoji)
}



onMounted(async () => {
  console.log('ChatSidebar: Setting up Echo listener on chat channel');

  // Load chat history first
  await loadChatHistory()

  const channel = echo.channel('chat');

  // Listen for all events on this channel for debugging
  channel.listen('*', (eventName: string, data: any) => {
    console.log('ChatSidebar: Received any event:', eventName, data);
  });

  channel.listen('.MessageSent', (e: { message: string, username: string, userId: number, timestamp: number }) => {
    console.log('ChatSidebar: Received .MessageSent event:', e);

    // Add all messages (including own) since we're not adding them locally anymore
    const newMsg = convertBroadcastMessageToMessage(e, authStore.user?.id || 0)
    messages.value.push(newMsg);
    scrollToBottom()
  });

  console.log('ChatSidebar: Echo listener setup complete');
})

onUnmounted(() => {
  console.log('ChatSidebar: Cleaning up Echo listener');
  // Clean up listeners
  echo.leave('chat');
  console.log('ChatSidebar: Echo listener cleanup complete');
})
</script>

<template>
  <div class="chat-sidebar">
    <div class="sidebar-header">
      <h3>Chat</h3>
      <button @click="closeChat" class="close-btn mobile-only">×</button>
    </div>

    <div class="sidebar-content">
      <div class="messages-container" ref="messagesContainer">
        <div v-if="isLoadingHistory" class="loading-messages">
          Loading chat history...
        </div>
        <div v-else-if="messages.length === 0" class="no-messages">
          No messages yet. Start the conversation!
        </div>
        <div v-else class="messages">
          <div
            v-for="(msg, index) in messages"
            :key="msg.id || index"
            class="message"
            :class="{ 'self': msg.isSelf }"
          >
            <div class="message-header" v-if="!msg.isSelf">
              <span class="username">{{ msg.username }}</span>
              <span class="timestamp" v-if="msg.timestamp">{{ formatTimestamp(msg.timestamp) }}</span>
            </div>
            <div class="message-content">
              {{ msg.text }}
            </div>
            <div class="message-timestamp" v-if="msg.isSelf && msg.timestamp">
              {{ formatTimestamp(msg.timestamp) }}
            </div>
          </div>
        </div>
      </div>

      <div class="message-input-container">
        <EmojiAutocomplete
          :emojis="filteredEmojis"
          :selectedIndex="autocompleteSelectedIndex"
          :isVisible="isAutocompleteVisible"
          @emoji-selected="onAutocompleteEmojiSelected"
        />

        <div class="message-input">
          <input
            ref="messageInput"
            v-model="newMessage"
            @input="handleInput"
            @keydown="handleKeyDown"
            placeholder="Type a message... (use :emoji: for emojis)"
            type="text"
          />
          <button @click="toggleEmojiPicker" class="emoji-button" title="Add emoji">
            😀
          </button>
          <button @click="sendMessage" :disabled="!newMessage.trim()">Send</button>
        </div>

        <EmojiPicker
          ref="emojiPicker"
          @emoji-selected="onEmojiSelected"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-sidebar {
  position: fixed;
  top: var(--header-height, 80px);
  left: 0;
  width: 320px;
  height: calc(100vh - var(--header-height, 80px));
  background-color: white;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 100;
  transition: width 0.3s ease;
}



.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  min-height: 60px;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: #e9ecef;
}

.mobile-only {
  display: none;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #fafafa;
}

.no-messages {
  color: #999;
  text-align: center;
  padding: 2rem 1rem;
  font-size: 0.9rem;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.message {
  max-width: 85%;
  align-self: flex-start;
}

.message.self {
  align-self: flex-end;
}

.message-header {
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.username {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timestamp {
  font-size: 0.7rem;
  color: #999;
  font-weight: normal;
  text-transform: none;
  letter-spacing: normal;
}

.message-timestamp {
  font-size: 0.7rem;
  color: #999;
  text-align: right;
  margin-top: 0.25rem;
}

.loading-messages {
  text-align: center;
  color: #999;
  padding: 2rem 1rem;
  font-style: italic;
  font-size: 0.9rem;
}

.message-content {
  padding: 0.5rem 0.75rem;
  border-radius: 12px;
  background-color: #e9ecef;
  color: #333;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.self .message-content {
  background-color: #42b883;
  color: white;
}

.message-input-container {
  position: relative;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

.message-input {
  display: flex;
  padding: 1rem;
  gap: 0.5rem;
}

.message-input input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 0.9rem;
  outline: none;
}

.message-input input:focus {
  border-color: #42b883;
}

.emoji-button {
  padding: 0.5rem;
  background: none;
  border: 1px solid #ddd;
  border-radius: 20px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.emoji-button:hover {
  background-color: #f8f9fa;
}

.message-input button {
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.message-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.message-input button:hover:not(:disabled) {
  background-color: #3aa876;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .chat-sidebar {
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
    border-right: none;
    box-shadow: none;
  }

  .mobile-only {
    display: flex;
  }
}
</style>
