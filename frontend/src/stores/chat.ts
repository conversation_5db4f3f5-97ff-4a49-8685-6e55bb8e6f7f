import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import axios from 'axios'
import echo from '@/services/echo'
import { useAuthStore } from './auth'
import type { Message, MessageHistory } from '@/types/message'
import { convertApiMessageToMessage, convertBroadcastMessageToMessage } from '@/types/message'

export const useChatStore = defineStore('chat', () => {
  // State
  const messages = ref<Message[]>([])
  const isVisible = ref(false)
  const isLoadingHistory = ref(false)
  const isLoadingMore = ref(false)
  const currentPage = ref(1)
  const lastPage = ref(1)
  const hasMoreMessages = computed(() => currentPage.value < lastPage.value)
  const isInitialized = ref(false)
  const echoChannel = ref<any>(null)

  // Get auth store
  const authStore = useAuthStore()

  // Persistence
  const CHAT_VISIBILITY_KEY = 'chat_visibility'

  // Initialize chat visibility from localStorage
  const initializeVisibility = () => {
    const saved = localStorage.getItem(CHAT_VISIBILITY_KEY)
    if (saved !== null) {
      isVisible.value = JSON.parse(saved)
    }
  }

  // Save visibility to localStorage
  const saveVisibility = () => {
    localStorage.setItem(CHAT_VISIBILITY_KEY, JSON.stringify(isVisible.value))
  }

  // Toggle chat visibility
  const toggleVisibility = () => {
    isVisible.value = !isVisible.value
    saveVisibility()
  }

  // Set chat visibility
  const setVisibility = (visible: boolean) => {
    isVisible.value = visible
    saveVisibility()
  }

  // Load chat history
  const loadChatHistory = async (page: number = 1) => {
    if (page === 1) {
      isLoadingHistory.value = true
    } else {
      isLoadingMore.value = true
    }

    try {
      const response = await axios.get<MessageHistory>(`${import.meta.env.VITE_API_URL}/api/messages/history?page=${page}`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      })

      const newMessages = response.data.data.map(apiMessage =>
        convertApiMessageToMessage(apiMessage, authStore.user?.id || 0)
      )

      if (page === 1) {
        // Reverse to show oldest first for initial load
        messages.value = newMessages.reverse()
        currentPage.value = response.data.current_page
        lastPage.value = response.data.last_page
      } else {
        // Prepend older messages (already in correct order from API)
        messages.value = [...newMessages.reverse(), ...messages.value]
        currentPage.value = response.data.current_page
      }

    } catch (error) {
      console.error('Failed to load chat history:', error)
    } finally {
      isLoadingHistory.value = false
      isLoadingMore.value = false
    }
  }

  // Load more messages (for lazy loading)
  const loadMoreMessages = async () => {
    if (hasMoreMessages.value && !isLoadingMore.value) {
      await loadChatHistory(currentPage.value + 1)
    }
  }

  // Send message with optimistic updates
  const sendMessage = async (messageText: string) => {
    if (!messageText.trim() || !authStore.user) return

    // Create optimistic message
    const tempId = `temp_${Date.now()}_${Math.random()}`
    const optimisticMessage: Message = {
      tempId,
      text: messageText,
      username: authStore.user.username,
      userId: authStore.user.id,
      isSelf: true,
      status: 'sending',
      profileImageUrl: authStore.user.profile_image_url
    }

    // Add optimistic message immediately
    messages.value.push(optimisticMessage)

    try {
      // Send to server
      await axios.post(`${import.meta.env.VITE_API_URL}/api/messages`, {
        message: messageText
      }, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      })

      // Update optimistic message status
      const messageIndex = messages.value.findIndex(m => m.tempId === tempId)
      if (messageIndex !== -1) {
        messages.value[messageIndex].status = 'sent'
      }

    } catch (error) {
      console.error('Failed to send message:', error)

      // Update optimistic message status to failed
      const messageIndex = messages.value.findIndex(m => m.tempId === tempId)
      if (messageIndex !== -1) {
        messages.value[messageIndex].status = 'failed'
      }
    }
  }

  // Handle incoming websocket message
  const handleIncomingMessage = (broadcastData: any) => {
    const newMessage = convertBroadcastMessageToMessage(broadcastData, authStore.user?.id || 0)

    // If this is our own message, replace the optimistic one
    if (newMessage.isSelf) {
      const optimisticIndex = messages.value.findIndex(m =>
        m.status === 'sent' &&
        m.text === newMessage.text &&
        m.isSelf &&
        !m.timestamp
      )

      if (optimisticIndex !== -1) {
        // Replace optimistic message with real one
        messages.value[optimisticIndex] = newMessage
        return
      }
    }

    // Add new message (from others or if optimistic wasn't found)
    messages.value.push(newMessage)
  }

  // Initialize Echo connection
  const initializeEcho = () => {
    if (echoChannel.value || !authStore.isAuthenticated) return

    console.log('Chat Store: Setting up Echo listener on chat channel')

    echoChannel.value = echo.channel('chat')

    echoChannel.value.listen('.MessageSent', (e: any) => {
      console.log('Chat Store: Received .MessageSent event:', e)
      handleIncomingMessage(e)
    })
  }

  // Cleanup Echo connection
  const cleanupEcho = () => {
    if (echoChannel.value) {
      echo.leave('chat')
      echoChannel.value = null
    }
  }

  // Initialize chat (load history and setup echo)
  const initialize = async () => {
    if (isInitialized.value || !authStore.isAuthenticated) return

    initializeVisibility()
    await loadChatHistory()
    initializeEcho()
    isInitialized.value = true
  }

  // Reset chat state (for logout)
  const reset = () => {
    messages.value = []
    currentPage.value = 1
    lastPage.value = 1
    isLoadingHistory.value = false
    isLoadingMore.value = false
    isInitialized.value = false
    cleanupEcho()
  }

  return {
    // State
    messages,
    isVisible,
    isLoadingHistory,
    isLoadingMore,
    hasMoreMessages,
    isInitialized,

    // Actions
    toggleVisibility,
    setVisibility,
    loadChatHistory,
    loadMoreMessages,
    sendMessage,
    initialize,
    reset,
    initializeVisibility
  }
})
